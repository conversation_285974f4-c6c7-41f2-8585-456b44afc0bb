package com.drawing.app.decorator;

import com.drawing.app.model.Shape;
import javafx.scene.paint.Color;

// Factory for creating decorated shapes - Singleton + Factory Pattern
public class DecoratorFactory {
    private static DecoratorFactory instance;
    
    // Private constructor for Singleton
    private DecoratorFactory() {}
    
    // Singleton getInstance method
    public static synchronized DecoratorFactory getInstance() {
        if (instance == null) {
            instance = new DecoratorFactory();
        }
        return instance;
    }
    
    public enum DecorationType {
        SHADOW, BORDER, GLOW
    }
    
    // Create a decorated shape
    public Shape createDecoratedShape(Shape baseShape, DecorationType type) {
        switch (type) {
            case SHADOW:
                return new ShadowDecorator(baseShape);
            case BORDER:
                return new BorderDecorator(baseShape);
            case GLOW:
                return new GlowDecorator(baseShape);
            default:
                return baseShape;
        }
    }
    
    // Create a decorated shape with custom parameters
    public Shape createShadowShape(Shape baseShape, double offsetX, double offsetY, Color shadowColor) {
        return new ShadowDecorator(baseShape, offsetX, offsetY, shadowColor);
    }
    
    public Shape createBorderShape(Shape baseShape, double borderWidth, Color borderColor, double padding) {
        return new BorderDecorator(baseShape, borderWidth, borderColor, padding);
    }
    
    public Shape createGlowShape(Shape baseShape, Color glowColor, int layers, double intensity) {
        return new GlowDecorator(baseShape, glowColor, layers, intensity);
    }
    
    // Create multiple decorations (chaining decorators)
    public Shape createMultiDecoratedShape(Shape baseShape, DecorationType... types) {
        Shape decoratedShape = baseShape;
        
        for (DecorationType type : types) {
            decoratedShape = createDecoratedShape(decoratedShape, type);
        }
        
        return decoratedShape;
    }
    
    // Remove decoration (unwrap decorator)
    public Shape removeDecoration(Shape decoratedShape) {
        if (decoratedShape instanceof ShapeDecorator) {
            return ((ShapeDecorator) decoratedShape).getDecoratedShape();
        }
        return decoratedShape;
    }
    
    // Remove all decorations (unwrap all decorators)
    public Shape removeAllDecorations(Shape decoratedShape) {
        Shape currentShape = decoratedShape;
        
        while (currentShape instanceof ShapeDecorator) {
            currentShape = ((ShapeDecorator) currentShape).getDecoratedShape();
        }
        
        return currentShape;
    }
    
    // Check if shape is decorated
    public boolean isDecorated(Shape shape) {
        return shape instanceof ShapeDecorator;
    }
    
    // Get decoration type
    public String getDecorationType(Shape shape) {
        if (shape instanceof ShadowDecorator) {
            return "Shadow";
        } else if (shape instanceof BorderDecorator) {
            return "Border";
        } else if (shape instanceof GlowDecorator) {
            return "Glow";
        } else if (shape instanceof ShapeDecorator) {
            return "Custom Decorator";
        } else {
            return "No Decoration";
        }
    }
}
