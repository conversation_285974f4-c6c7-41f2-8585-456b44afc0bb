# Logging Strategies Setup and Testing Guide

This guide will help you set up and test all three logging strategies in the Drawing Application.

## Prerequisites

1. **Java 11 or higher** installed
2. **MySQL Server** installed and running (for database logging)
3. **Internet connection** (for downloading dependencies)

## Step 1: MySQL Database Setup

### Option A: Using MySQL Command Line
1. Open MySQL command line client
2. Login with your credentials (usually root)
3. Run the following commands:
```sql
CREATE DATABASE IF NOT EXISTS drawing_app;
USE drawing_app;

-- Create logs table for logging
CREATE TABLE IF NOT EXISTS logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME,
    message TEXT
);

-- Verify table creation
SHOW TABLES;
DESCRIBE logs;
```

### Option B: Using phpMyAdmin
1. Open phpMyAdmin in your browser (usually http://localhost/phpmyadmin)
2. Click "New" to create a new database
3. Name it `drawing_app`
4. Click "Create"
5. Select the database and go to SQL tab
6. Run the SQL commands from Option A

### Option C: Run the existing setup script
```bash
mysql -u root -p < database_setup.sql
```

## Step 2: Update Database Credentials (if needed)

If your MySQL setup uses different credentials, update them in:
`src/main/java/com/drawing/app/controller/DrawingController.java`

Look for this section and update as needed:
```java
DatabaseLoggingStrategy dbStrategy = new DatabaseLoggingStrategy(
    "***************************************",
    "root",        // Change this to your MySQL username
    "password"     // Change this to your MySQL password
);
```

## Step 3: Test Individual Logging Strategies

### Quick Test
Run the logging test script:
```bash
test_logging.bat
```

This will:
1. Download required dependencies
2. Compile the test classes
3. Run tests for all three logging strategies
4. Show results for each strategy

### Manual Testing

#### Console Logging Test
1. Run the application: `run.bat`
2. In the toolbar, select "Console" from the Logging dropdown
3. Draw some shapes
4. Save/load drawings
5. Check the console output for timestamped log messages

#### File Logging Test
1. Run the application: `run.bat`
2. In the toolbar, select "File" from the Logging dropdown
3. Draw some shapes
4. Save/load drawings
5. Check for `drawing_log.txt` file in the project directory
6. Open the file to verify log entries with timestamps

#### Database Logging Test
1. Ensure MySQL is running and database is set up
2. Run the application: `run.bat`
3. In the toolbar, select "Database" from the Logging dropdown
4. Check console for connection status message
5. Draw some shapes
6. Save/load drawings
7. Check the database:
```sql
USE drawing_app;
SELECT * FROM logs ORDER BY timestamp DESC;
```

## Step 4: Troubleshooting

### MySQL Driver Issues
**Problem**: "MySQL driver not found" message
**Solution**: 
- Delete the existing `mysql-connector-java-8.0.33.jar` file
- Run `run.bat` again to re-download
- Check your internet connection

### Database Connection Issues
**Problem**: "Database connection failed" message
**Solutions**:
1. Verify MySQL server is running
2. Check if database `drawing_app` exists
3. Verify username/password in the code
4. Try connecting manually: `mysql -u root -p`

### File Logging Issues
**Problem**: Log file not created
**Solutions**:
1. Check file permissions in the project directory
2. Ensure the application has write access
3. Look for error messages in console

## Step 5: Verify All Strategies Work

### Complete Test Procedure
1. **Start the application**: `run.bat`
2. **Test Console Logging**:
   - Select "Console" from logging dropdown
   - Draw a rectangle
   - Verify message appears in console with timestamp
3. **Test File Logging**:
   - Select "File" from logging dropdown
   - Draw a circle
   - Check `drawing_log.txt` file exists and contains the entry
4. **Test Database Logging**:
   - Select "Database" from logging dropdown
   - Draw a line
   - Check database table for the entry:
   ```sql
   SELECT * FROM logs WHERE message LIKE '%line%';
   ```

### Expected Results
- **Console**: Messages appear immediately in the console window
- **File**: Messages are appended to `drawing_log.txt` with timestamps
- **Database**: Messages are stored in the `logs` table with timestamps

## Common Issues and Solutions

| Issue | Cause | Solution |
|-------|-------|----------|
| MySQL driver not found | JAR file corrupted/missing | Delete JAR and re-run `run.bat` |
| Database connection failed | MySQL not running | Start MySQL service |
| Access denied for user | Wrong credentials | Update username/password in code |
| Table doesn't exist | Database not set up | Run database setup SQL |
| File not created | Permission issues | Check directory write permissions |

## Testing Commands Summary

```bash
# Test all logging strategies
test_logging.bat

# Run the main application
run.bat

# Check database logs (in MySQL)
mysql -u root -p
USE drawing_app;
SELECT * FROM logs ORDER BY timestamp DESC LIMIT 10;
```

## Success Indicators

✅ **Console Logging**: Messages appear in console with format `[YYYY-MM-DD HH:MM:SS] message`
✅ **File Logging**: `drawing_log.txt` file created with timestamped entries
✅ **Database Logging**: Records appear in `drawing_app.logs` table
✅ **Strategy Switching**: Can switch between strategies without errors
✅ **Error Handling**: Graceful fallback when database is unavailable
