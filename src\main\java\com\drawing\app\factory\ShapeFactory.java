package com.drawing.app.factory;

import com.drawing.app.model.*;
import javafx.scene.paint.Color;

// Factory Pattern + Singleton Pattern
public class ShapeFactory {
    private static ShapeFactory instance;

    // Private constructor for Singleton
    private ShapeFactory() {}

    // Singleton getInstance method
    public static synchronized ShapeFactory getInstance() {
        if (instance == null) {
            instance = new ShapeFactory();
        }
        return instance;
    }

    public enum ShapeType {
        RECTANGLE, CIRCLE, LINE
    }

    public Shape createShape(ShapeType type, double x, double y, double... params) {
        Color defaultColor = Color.BLACK;
        double defaultStrokeWidth = 2.0;
        
        switch (type) {
            case RECTANGLE:
                double width = params.length > 0 ? params[0] : 50;
                double height = params.length > 1 ? params[1] : 30;
                return new Rectangle(x, y, width, height, defaultColor, defaultStrokeWidth);
                
            case CIRCLE:
                double radius = params.length > 0 ? params[0] : 25;
                return new Circle(x, y, radius, defaultColor, defaultStrokeWidth);
                
            case LINE:
                double endX = params.length > 0 ? params[0] : x + 50;
                double endY = params.length > 1 ? params[1] : y + 50;
                return new Line(x, y, endX, endY, defaultColor, defaultStrokeWidth);
                
            default:
                throw new IllegalArgumentException("Unknown shape type: " + type);
        }
    }
    
    public static Shape createShape(ShapeType type, double x, double y, Color color, double strokeWidth, double... params) {
        Shape shape = getInstance().createShape(type, x, y, params);
        shape.setColor(color);
        shape.setStrokeWidth(strokeWidth);
        return shape;
    }
}
