package com.drawing.app.decorator;

import com.drawing.app.model.Shape;
import com.drawing.app.model.Rectangle;
import com.drawing.app.model.Circle;
import com.drawing.app.model.Line;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

// Concrete Decorator - Adds decorative border around shapes
public class BorderDecorator extends ShapeDecorator {
    private final double borderWidth;
    private final Color borderColor;
    private final double padding;
    
    public BorderDecorator(Shape decoratedShape) {
        this(decoratedShape, 3, Color.BLUE, 10);
    }
    
    public BorderDecorator(Shape decoratedShape, double borderWidth, Color borderColor, double padding) {
        super(decoratedShape);
        this.borderWidth = borderWidth;
        this.borderColor = borderColor;
        this.padding = padding;
    }
    
    @Override
    protected void addDecoration(GraphicsContext gc) {
        // Save current graphics state
        gc.save();
        
        // Set border properties
        gc.setStroke(borderColor);
        gc.setLineWidth(borderWidth);
        
        // Draw border based on shape type
        if (decoratedShape instanceof Rectangle) {
            drawRectangleBorder(gc, (Rectangle) decoratedShape);
        } else if (decoratedShape instanceof Circle) {
            drawCircleBorder(gc, (Circle) decoratedShape);
        } else if (decoratedShape instanceof Line) {
            drawLineBorder(gc, (Line) decoratedShape);
        }
        
        // Restore graphics state
        gc.restore();
    }
    
    private void drawRectangleBorder(GraphicsContext gc, Rectangle rect) {
        double x = rect.getX() - padding;
        double y = rect.getY() - padding;
        double width = rect.getWidth() + 2 * padding;
        double height = rect.getHeight() + 2 * padding;
        gc.strokeRect(x, y, width, height);
    }
    
    private void drawCircleBorder(GraphicsContext gc, Circle circle) {
        double radius = circle.getRadius() + padding;
        double x = circle.getX() - radius;
        double y = circle.getY() - radius;
        double diameter = radius * 2;
        gc.strokeOval(x, y, diameter, diameter);
    }
    
    private void drawLineBorder(GraphicsContext gc, Line line) {
        // For lines, draw a rectangular border around the line
        double minX = Math.min(line.getX(), line.getEndX()) - padding;
        double minY = Math.min(line.getY(), line.getEndY()) - padding;
        double maxX = Math.max(line.getX(), line.getEndX()) + padding;
        double maxY = Math.max(line.getY(), line.getEndY()) + padding;
        
        gc.strokeRect(minX, minY, maxX - minX, maxY - minY);
    }
    
    // Getters for border properties
    public double getBorderWidth() {
        return borderWidth;
    }
    
    public Color getBorderColor() {
        return borderColor;
    }
    
    public double getPadding() {
        return padding;
    }
}
