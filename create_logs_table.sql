-- Create logs table for the logging system
-- Run this in your drawing_app database

USE drawing_app;

-- Create logs table for logging strategies
CREATE TABLE IF NOT EXISTS logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert a test record to verify the table works
INSERT INTO logs (timestamp, message) VALUES (NOW(), 'Logs table created successfully');

-- Show the table structure
DESCRIBE logs;

-- Show all logs
SELECT * FROM logs ORDER BY timestamp DESC;
