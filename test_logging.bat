@echo off
echo ===============================================
echo Testing Logging Strategies for Drawing App
echo ===============================================

REM Download JavaFX if not present
if not exist "javafx-sdk" (
    echo Downloading JavaFX SDK...
    powershell -Command "Invoke-WebRequest -Uri 'https://download2.gluonhq.com/openjfx/17.0.2/openjfx-17.0.2_windows-x64_bin-sdk.zip' -OutFile 'javafx-sdk.zip'"
    powershell -Command "Expand-Archive -Path 'javafx-sdk.zip' -DestinationPath '.'"
    ren javafx-sdk-17.0.2 javafx-sdk
    del javafx-sdk.zip
)

REM Download MySQL Connector if not present
if not exist "mysql-connector-java-8.0.33.jar" (
    echo Downloading MySQL Connector...
    powershell -Command "try { [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar' -OutFile 'mysql-connector-java-8.0.33.jar' -UseBasicParsing } catch { Write-Host 'Download failed, continuing without MySQL driver' }"
)

REM Create target/classes directory if it doesn't exist
if not exist "target\classes" mkdir target\classes

REM Compile the project including the test class
echo Compiling project and test classes...
javac --module-path javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -cp "src/main/java;mysql-connector-java-8.0.33.jar" -d target/classes src/main/java/com/drawing/app/logging/*.java 2>nul

REM Run the logging test
echo.
echo Running logging tests...
echo.
java -cp "target/classes;mysql-connector-java-8.0.33.jar" com.drawing.app.logging.LoggingTest

echo.
echo ===============================================
echo Test completed. Check the output above.
echo ===============================================
pause
