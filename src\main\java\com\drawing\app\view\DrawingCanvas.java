package com.drawing.app.view;

import com.drawing.app.factory.ShapeFactory;
import com.drawing.app.model.Circle;
import com.drawing.app.model.Drawing;
import com.drawing.app.model.Line;
import com.drawing.app.model.Rectangle;
import com.drawing.app.model.Shape;

import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.input.MouseEvent;
import javafx.scene.paint.Color;

public class DrawingCanvas extends Canvas {
    private Drawing drawing;
    private GraphicsContext gc;
    
    // Drawing state
    private boolean isDrawing = false;
    private boolean isDragging = false;
    private boolean isRotating = false;
    private Shape currentShape = null;
    private Shape selectedShape = null;
    private double startX, startY;
    private double lastMouseX, lastMouseY;
    
    // Drawing mode
    private ShapeFactory.ShapeType currentShapeType = ShapeFactory.ShapeType.RECTANGLE;
    private Color currentColor = Color.BLACK;
    private double currentStrokeWidth = 2.0;
    
    // Callback for shape creation
    private ShapeCreationCallback shapeCreationCallback;
    
    public interface ShapeCreationCallback {
        void onShapeCreated(Shape shape);
    }
    
    public DrawingCanvas(double width, double height) {
        super(width, height);
        this.drawing = new Drawing();
        this.gc = getGraphicsContext2D();
        
        setupMouseEvents();
        redraw();
    }
    
    private void setupMouseEvents() {
        setOnMousePressed(this::handleMousePressed);
        setOnMouseDragged(this::handleMouseDragged);
        setOnMouseReleased(this::handleMouseReleased);
        setOnMouseMoved(this::handleMouseMoved);
    }
    
    private void handleMousePressed(MouseEvent event) {
        double mouseX = event.getX();
        double mouseY = event.getY();
        
        // Check if clicking on rotation handle
        if (selectedShape != null && isOnRotationHandle(selectedShape, mouseX, mouseY)) {
            isRotating = true;
            lastMouseX = mouseX;
            lastMouseY = mouseY;
            return;
        }
        
        // Check if clicking on existing shape
        Shape clickedShape = findShapeAt(mouseX, mouseY);
        if (clickedShape != null) {
            // Deselect previous shape
            if (selectedShape != null) {
                selectedShape.setSelected(false);
            }
            
            // Select new shape
            selectedShape = clickedShape;
            selectedShape.setSelected(true);
            isDragging = true;
            startX = mouseX;
            startY = mouseY;
            redraw();
            return;
        }
        
        // Deselect if clicking on empty space
        if (selectedShape != null) {
            selectedShape.setSelected(false);
            selectedShape = null;
            redraw();
        }
        
        // Start creating new shape
        isDrawing = true;
        startX = mouseX;
        startY = mouseY;
        
        // Create initial shape
        currentShape = createInitialShape(mouseX, mouseY);
    }
    
    private void handleMouseDragged(MouseEvent event) {
        double mouseX = event.getX();
        double mouseY = event.getY();
        
        if (isRotating && selectedShape != null) {
            // Calculate rotation
            double centerX = selectedShape.getCenterX();
            double centerY = selectedShape.getCenterY();
            
            double angle1 = Math.atan2(lastMouseY - centerY, lastMouseX - centerX);
            double angle2 = Math.atan2(mouseY - centerY, mouseX - centerX);
            double deltaAngle = Math.toDegrees(angle2 - angle1);
            
            selectedShape.rotate(deltaAngle);
            lastMouseX = mouseX;
            lastMouseY = mouseY;
            redraw();
            return;
        }
        
        if (isDragging && selectedShape != null) {
            // Move selected shape
            double deltaX = mouseX - startX;
            double deltaY = mouseY - startY;
            selectedShape.move(deltaX, deltaY);
            startX = mouseX;
            startY = mouseY;
            redraw();
            return;
        }
        
        if (isDrawing && currentShape != null) {
            // Update shape size based on mouse position
            updateShapeSize(currentShape, startX, startY, mouseX, mouseY);
            redraw();
        }
    }
    
    private void handleMouseReleased(MouseEvent event) {
        if (isRotating) {
            isRotating = false;
            return;
        }
        
        if (isDragging) {
            isDragging = false;
            return;
        }          if (isDrawing && currentShape != null) {
            Shape createdShape = currentShape;
            currentShape = null;
            isDrawing = false;
            // Finalize shape creation through callback
            if (shapeCreationCallback != null) {
                shapeCreationCallback.onShapeCreated(createdShape);
            }
            redraw();
        }
    }
    
    private void handleMouseMoved(MouseEvent event) {
        // Update cursor based on what's under mouse
        // This could be enhanced to show different cursors for resize handles, etc.
    }
    
    private Shape createInitialShape(double x, double y) {
        switch (currentShapeType) {
            case RECTANGLE:
                return new Rectangle(x, y, 0, 0, currentColor, currentStrokeWidth);
            case CIRCLE:
                return new Circle(x, y, 0, currentColor, currentStrokeWidth);
            case LINE:
                return new Line(x, y, x, y, currentColor, currentStrokeWidth);
            default:
                return null;
        }
    }
    
    private void updateShapeSize(Shape shape, double startX, double startY, double currentX, double currentY) {
        if (shape instanceof Rectangle) {
            Rectangle rect = (Rectangle) shape;
            double width = currentX - startX;
            double height = currentY - startY;
            
            // Adjust position if dragging from right to left or bottom to top
            if (width < 0) {
                rect.setX(currentX);
                width = -width;
            } else {
                rect.setX(startX);
            }
            
            if (height < 0) {
                rect.setY(currentY);
                height = -height;
            } else {
                rect.setY(startY);
            }
            
            rect.updateSize(width, height);
            
        } else if (shape instanceof Circle) {
            Circle circle = (Circle) shape;
            double dx = currentX - startX;
            double dy = currentY - startY;
            double radius = Math.sqrt(dx * dx + dy * dy);
            circle.updateRadius(radius);
            
        } else if (shape instanceof Line) {
            Line line = (Line) shape;
            line.updateEndPoint(currentX, currentY);
        }
    }
    
    private Shape findShapeAt(double x, double y) {
        // Search from top to bottom (last drawn first)
        for (int i = drawing.getShapes().size() - 1; i >= 0; i--) {
            Shape shape = drawing.getShapes().get(i);
            if (shape.contains(x, y)) {
                return shape;
            }
        }
        return null;
    }
    
    private boolean isOnRotationHandle(Shape shape, double x, double y) {
        double centerX = shape.getCenterX();
        double centerY;
        
        if (shape instanceof Rectangle) {
            centerY = shape.getY() - 20;
        } else if (shape instanceof Circle) {
            Circle circle = (Circle) shape;
            centerY = circle.getY() - circle.getRadius() - 20;
        } else if (shape instanceof Line) {
            centerY = shape.getCenterY() - 20;
        } else {
            return false;
        }
        
        double dx = x - centerX;
        double dy = y - centerY;
        return Math.sqrt(dx * dx + dy * dy) <= 6;
    }
    
    public void redraw() {
        // Clear canvas
        gc.clearRect(0, 0, getWidth(), getHeight());
        gc.setFill(Color.WHITE);
        gc.fillRect(0, 0, getWidth(), getHeight());
        
        // Draw all shapes in the drawing
        for (Shape shape : drawing.getShapes()) {
            shape.render(gc);
        }
        
        // Draw current shape being created
        if (isDrawing && currentShape != null) {
            currentShape.render(gc);
        }
    }
    
    // Getters and setters
    public Drawing getDrawing() {
        return drawing;
    }      public void setDrawing(Drawing drawing) {
        this.drawing = drawing;
        selectedShape = null;
        isDrawing = false;
        currentShape = null;
        redraw();
    }
    
    public void setCurrentShapeType(ShapeFactory.ShapeType shapeType) {
        this.currentShapeType = shapeType;
    }
    
    public void setCurrentColor(Color color) {
        this.currentColor = color;
    }
    
    public void setCurrentStrokeWidth(double strokeWidth) {
        this.currentStrokeWidth = strokeWidth;
    }
    
    public void setShapeCreationCallback(ShapeCreationCallback callback) {
        this.shapeCreationCallback = callback;
    }
    
    public void deleteSelectedShape() {
        if (selectedShape != null) {
            drawing.removeShape(selectedShape);
            selectedShape = null;
            redraw();
        }
    }

    public Shape getSelectedShape() {
        return selectedShape;
    }
}
