package com.drawing.app.decorator;

import com.drawing.app.model.Shape;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

// Decorator Pattern - Base Decorator for Shapes
public abstract class ShapeDecorator extends Shape {
    protected Shape decoratedShape;
    
    public ShapeDecorator(Shape decoratedShape) {
        super(decoratedShape.getX(), decoratedShape.getY(), 
              decoratedShape.getColor(), decoratedShape.getStrokeWidth());
        this.decoratedShape = decoratedShape;
    }
    
    // Override the draw method instead of render (since render is final)
    @Override
    protected void draw(GraphicsContext gc) {
        // First draw the base shape
        if (decoratedShape instanceof ShapeDecorator) {
            ((ShapeDecorator) decoratedShape).draw(gc);
        } else {
            // Use reflection to access protected draw method
            try {
                java.lang.reflect.Method drawMethod = decoratedShape.getClass().getDeclaredMethod("draw", GraphicsContext.class);
                drawMethod.setAccessible(true);
                drawMethod.invoke(decoratedShape, gc);
            } catch (Exception e) {
                // Fallback: render the whole shape
                decoratedShape.render(gc);
                return;
            }
        }
        // Then add decorative elements
        addDecoration(gc);
    }

    // Template method for adding decoration
    protected abstract void addDecoration(GraphicsContext gc);
    
    @Override
    public boolean contains(double x, double y) {
        return decoratedShape.contains(x, y);
    }
    
    @Override
    public double getCenterX() {
        return decoratedShape.getCenterX();
    }
    
    @Override
    public double getCenterY() {
        return decoratedShape.getCenterY();
    }
    
    @Override
    protected void drawSelectionHandles(GraphicsContext gc) {
        // Use reflection to access protected drawSelectionHandles method
        try {
            java.lang.reflect.Method method = decoratedShape.getClass().getDeclaredMethod("drawSelectionHandles", GraphicsContext.class);
            method.setAccessible(true);
            method.invoke(decoratedShape, gc);
        } catch (Exception e) {
            // Fallback: draw simple selection handles
            if (decoratedShape.isSelected()) {
                gc.setStroke(javafx.scene.paint.Color.BLUE);
                gc.setLineWidth(1);
                double centerX = decoratedShape.getCenterX();
                double centerY = decoratedShape.getCenterY();
                gc.strokeOval(centerX - 5, centerY - 5, 10, 10);
            }
        }
    }
    
    // Delegate property access
    @Override
    public double getX() {
        return decoratedShape.getX();
    }
    
    @Override
    public double getY() {
        return decoratedShape.getY();
    }
    
    @Override
    public Color getColor() {
        return decoratedShape.getColor();
    }
    
    @Override
    public double getStrokeWidth() {
        return decoratedShape.getStrokeWidth();
    }
    
    @Override
    public double getRotation() {
        return decoratedShape.getRotation();
    }
    
    @Override
    public boolean isSelected() {
        return decoratedShape.isSelected();
    }
    
    @Override
    public void setX(double x) {
        decoratedShape.setX(x);
    }
    
    @Override
    public void setY(double y) {
        decoratedShape.setY(y);
    }
    
    @Override
    public void setColor(Color color) {
        decoratedShape.setColor(color);
    }
    
    @Override
    public void setStrokeWidth(double strokeWidth) {
        decoratedShape.setStrokeWidth(strokeWidth);
    }
    
    @Override
    public void setRotation(double rotation) {
        decoratedShape.setRotation(rotation);
    }
    
    @Override
    public void setSelected(boolean selected) {
        decoratedShape.setSelected(selected);
    }
    
    @Override
    public void move(double deltaX, double deltaY) {
        decoratedShape.move(deltaX, deltaY);
    }
    
    @Override
    public void rotate(double angle) {
        decoratedShape.rotate(angle);
    }
    
    // Get the underlying shape
    public Shape getDecoratedShape() {
        return decoratedShape;
    }
}
