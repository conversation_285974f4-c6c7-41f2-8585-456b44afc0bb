# RAPPORT DU PROJET - APPLICATION DE DESSIN
## MASI Mini Projet

---

## I. Introduction

### Présentation du Projet
Ce projet consiste en une **application de dessin interactive** développée en Java avec JavaFX. L'application permet aux utilisateurs de créer, modifier, sauvegarder et charger des dessins composés de formes géométriques (rectangles, cercles, lignes). 

### Objectifs Principaux
- **Création de formes géométriques** : Rectangle, Cercle, Ligne
- **Gestion des dessins** : Sauvegarde/Chargement (fichier et base de données)
- **Fonctionnalités avancées** : Undo/Redo, sélection, rotation, décoration
- **Système de logging** : Console, fichier, base de données MySQL
- **Fonctionnalités graphiques** : Création de graphes avec nœuds et arêtes, algorithme de plus court chemin

### Technologies Utilisées
- **Langage** : Java 11+
- **Interface graphique** : JavaFX
- **Base de données** : MySQL
- **Architecture** : MVC (Model-View-Controller)
- **Patterns de conception** : Singleton, Factory, Command, Observer, Strategy, Decorator

---

## II. Structure des Packages

### 📦 `com.drawing.app`
**Package racine** contenant la classe principale `Main.java` qui lance l'application JavaFX.

### 📦 `com.drawing.app.model`
**Modèle de données** - Contient toutes les entités métier :
- `Shape.java` : Classe abstraite pour toutes les formes
- `Rectangle.java`, `Circle.java`, `Line.java` : Implémentations concrètes des formes
- `Drawing.java` : Conteneur pour une collection de formes
- `Graph.java`, `Node.java`, `Edge.java` : Modèle pour les graphes

### 📦 `com.drawing.app.view`
**Interface utilisateur** - Composants visuels JavaFX :
- `MainView.java` : Vue principale avec toolbar et layout
- `DrawingCanvas.java` : Canvas de dessin interactif
- `GraphPanel.java` : Panneau pour les opérations sur graphes

### 📦 `com.drawing.app.controller`
**Contrôleurs** - Logique de contrôle MVC :
- `DrawingController.java` : Contrôleur principal pour le dessin
- `GraphController.java` : Contrôleur pour les fonctionnalités graphiques

### 📦 `com.drawing.app.command`
**Pattern Command** - Gestion des commandes undo/redo :
- `Command.java` : Interface pour les commandes
- `CommandManager.java` : Gestionnaire des commandes (Singleton)
- `DrawShapeCommand.java` : Commande pour dessiner une forme

### 📦 `com.drawing.app.factory`
**Pattern Factory** - Création d'objets :
- `ShapeFactory.java` : Factory pour créer les formes (Singleton)

### 📦 `com.drawing.app.decorator`
**Pattern Decorator** - Décoration des formes :
- `ShapeDecorator.java` : Décorateur abstrait
- `ShadowDecorator.java`, `BorderDecorator.java`, `GlowDecorator.java` : Décorateurs concrets
- `DecoratorFactory.java` : Factory pour les décorateurs

### 📦 `com.drawing.app.observer`
**Pattern Observer** - Notification d'événements :
- `DrawingObserver.java` : Interface observateur
- `DrawingSubject.java` : Sujet observable

### 📦 `com.drawing.app.logging`
**Pattern Strategy** - Système de logging :
- `LoggingStrategy.java` : Interface pour les stratégies de logging
- `ConsoleLoggingStrategy.java`, `FileLoggingStrategy.java`, `DatabaseLoggingStrategy.java` : Stratégies concrètes
- `Logger.java` : Contexte du pattern Strategy

### 📦 `com.drawing.app.persistence`
**Pattern DAO** - Persistance des données :
- `DrawingDAO.java` : Interface DAO
- `FileDrawingDAO.java` : Implémentation fichier
- `MySQLDrawingDAO.java` : Implémentation base de données

---

## III. Diagramme Use Case UML

### Acteur Principal : **Utilisateur**

### Use Cases :

#### 🎨 **Gestion du Dessin**
- **Créer un nouveau dessin**
- **Ouvrir un dessin existant**
- **Sauvegarder un dessin**
- **Sauvegarder sous un nouveau nom**

#### ✏️ **Création de Formes**
- **Dessiner un rectangle**
- **Dessiner un cercle**
- **Dessiner une ligne**
- **Sélectionner une couleur**
- **Modifier l'épaisseur du trait**

#### 🔧 **Manipulation des Formes**
- **Sélectionner une forme**
- **Déplacer une forme**
- **Faire pivoter une forme**
- **Supprimer une forme**

#### 🎭 **Décoration des Formes**
- **Ajouter une ombre**
- **Ajouter une bordure**
- **Ajouter un effet de lueur**
- **Supprimer les décorations**

#### ↩️ **Gestion des Actions**
- **Annuler (Undo)**
- **Refaire (Redo)**

#### 📊 **Fonctionnalités Graphiques**
- **Créer un nœud**
- **Créer une arête**
- **Calculer le plus court chemin**

#### 📝 **Système de Logging**
- **Changer la stratégie de logging**
- **Consulter les logs**

---

## IV. Design Patterns Utilisés

### 1. 🏗️ **Pattern Singleton**

**Utilisation** : CommandManager, ShapeFactory, DecoratorFactory

**Objectif** : Garantir qu'une seule instance de ces classes existe dans l'application.

#### Diagramme UML - Singleton Pattern

```
┌─────────────────────────────┐
│      CommandManager         │
├─────────────────────────────┤
│ - instance: CommandManager  │
│ - undoStack: Stack<Command> │
│ - redoStack: Stack<Command> │
├─────────────────────────────┤
│ - CommandManager()          │
│ + getInstance(): CommandManager │
│ + executeCommand(Command)   │
│ + undo(): void              │
│ + redo(): void              │
└─────────────────────────────┘
```

**Implémentation** :
- Constructeur privé
- Méthode statique `getInstance()` synchronisée
- Instance statique privée

### 2. 🏭 **Pattern Factory**

**Utilisation** : ShapeFactory, DecoratorFactory

**Objectif** : Centraliser la création d'objets complexes.

#### Diagramme UML - Factory Pattern

```
┌─────────────────────────────┐
│       ShapeFactory          │
├─────────────────────────────┤
│ - instance: ShapeFactory    │
│ + ShapeType: enum           │
├─────────────────────────────┤
│ + getInstance(): ShapeFactory │
│ + createShape(type, x, y, params): Shape │
└─────────────────────────────┘
                │
                │ creates
                ▼
┌─────────────────────────────┐
│          Shape              │
├─────────────────────────────┤
│ # x, y: double              │
│ # color: Color              │
│ # strokeWidth: double       │
├─────────────────────────────┤
│ + render(GraphicsContext)   │
│ + draw(GraphicsContext)*    │
│ + contains(x, y): boolean*  │
└─────────────────────────────┘
                △
                │
    ┌───────────┼───────────┐
    │           │           │
┌───────┐  ┌─────────┐  ┌──────┐
│Rectangle│ │ Circle  │  │ Line │
└───────┘  └─────────┘  └──────┘
```

### 3. 📋 **Pattern Command**

**Utilisation** : Système Undo/Redo

**Objectif** : Encapsuler les actions en objets pour permettre l'annulation.

#### Diagramme UML - Command Pattern

```
┌─────────────────────────────┐
│         Command             │
├─────────────────────────────┤
│ + execute(): void           │
│ + undo(): void              │
└─────────────────────────────┘
                △
                │
┌─────────────────────────────┐
│     DrawShapeCommand        │
├─────────────────────────────┤
│ - drawing: Drawing          │
│ - shape: Shape              │
├─────────────────────────────┤
│ + execute(): void           │
│ + undo(): void              │
└─────────────────────────────┘

┌─────────────────────────────┐
│      CommandManager         │
├─────────────────────────────┤
│ - undoStack: Stack<Command> │
│ - redoStack: Stack<Command> │
├─────────────────────────────┤
│ + executeCommand(Command)   │
│ + undo(): void              │
│ + redo(): void              │
└─────────────────────────────┘

### 4. 👁️ **Pattern Observer**

**Utilisation** : Système de notification d'événements

**Objectif** : Notifier automatiquement les observateurs des changements.

#### Diagramme UML - Observer Pattern

```
┌─────────────────────────────┐
│     DrawingObserver         │
├─────────────────────────────┤
│ + onShapeSelected(String)   │
│ + onShapeDrawn(String)      │
│ + onDrawingSaved(String)    │
│ + onDrawingLoaded(String)   │
└─────────────────────────────┘
                △
                │ implements
┌─────────────────────────────┐
│         Logger              │
├─────────────────────────────┤
│ - strategy: LoggingStrategy │
├─────────────────────────────┤
│ + onShapeSelected(String)   │
│ + onShapeDrawn(String)      │
│ + onDrawingSaved(String)    │
│ + onDrawingLoaded(String)   │
└─────────────────────────────┘

┌─────────────────────────────┐
│      DrawingSubject         │
├─────────────────────────────┤
│ - observers: List<Observer> │
├─────────────────────────────┤
│ + addObserver(Observer)     │
│ + removeObserver(Observer)  │
│ # notifyShapeSelected()     │
│ # notifyShapeDrawn()        │
│ # notifyDrawingSaved()      │
│ # notifyDrawingLoaded()     │
└─────────────────────────────┘
                △
                │ extends
┌─────────────────────────────┐
│    DrawingController        │
├─────────────────────────────┤
│ - logger: Logger            │
│ - currentDrawing: Drawing   │
├─────────────────────────────┤
│ + selectShape(ShapeType)    │
│ + saveDrawing()             │
│ + openDrawing()             │
└─────────────────────────────┘
```

### 5. 🎯 **Pattern Strategy**

**Utilisation** : Système de logging

**Objectif** : Permettre de changer d'algorithme de logging à l'exécution.

#### Diagramme UML - Strategy Pattern

```
┌─────────────────────────────┐
│     LoggingStrategy         │
├─────────────────────────────┤
│ + log(String): void         │
│ + close(): void             │
└─────────────────────────────┘
                △
                │ implements
    ┌───────────┼───────────┐
    │           │           │
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ConsoleLogging│ │FileLogging  │ │DatabaseLogging│
│Strategy     │ │Strategy     │ │Strategy     │
├─────────────┤ ├─────────────┤ ├─────────────┤
│+ log(String)│ │- writer:    │ │- connection:│
│+ close()    │ │  PrintWriter│ │  Connection │
└─────────────┘ │+ log(String)│ │+ log(String)│
                │+ close()    │ │+ close()    │
                └─────────────┘ └─────────────┘

┌─────────────────────────────┐
│         Logger              │
├─────────────────────────────┤
│ - strategy: LoggingStrategy │
├─────────────────────────────┤
│ + setStrategy(LoggingStrategy) │
│ + log(String): void         │
└─────────────────────────────┘

### 6. 🎨 **Pattern Decorator**

**Utilisation** : Décoration des formes (ombre, bordure, lueur)

**Objectif** : Ajouter dynamiquement des fonctionnalités aux objets.

#### Diagramme UML - Decorator Pattern

```
┌─────────────────────────────┐
│          Shape              │
├─────────────────────────────┤
│ # x, y: double              │
│ # color: Color              │
├─────────────────────────────┤
│ + render(GraphicsContext)   │
│ + draw(GraphicsContext)*    │
└─────────────────────────────┘
                △
                │
    ┌───────────┼───────────┐
    │           │           │
┌─────────────┐ ┌─────────────────────────────┐
│  Rectangle  │ │      ShapeDecorator         │
│   Circle    │ ├─────────────────────────────┤
│    Line     │ │ # decoratedShape: Shape     │
└─────────────┘ ├─────────────────────────────┤
                │ + render(GraphicsContext)   │
                │ + draw(GraphicsContext)*    │
                └─────────────────────────────┘
                                △
                                │ extends
                    ┌───────────┼───────────┐
                    │           │           │
            ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
            │ShadowDecorator│ │BorderDecorator│ │GlowDecorator│
            ├─────────────┤ ├─────────────┤ ├─────────────┤
            │+ draw(gc)   │ │+ draw(gc)   │ │+ draw(gc)   │
            └─────────────┘ └─────────────┘ └─────────────┘
```

### 7. 🗃️ **Pattern DAO (Data Access Object)**

**Utilisation** : Persistance des dessins

**Objectif** : Séparer la logique d'accès aux données.

#### Diagramme UML - DAO Pattern

```
┌─────────────────────────────┐
│        DrawingDAO           │
├─────────────────────────────┤
│ + save(Drawing): void       │
│ + load(String): Drawing     │
│ + getAllDrawingNames(): List│
│ + delete(String): void      │
└─────────────────────────────┘
                △
                │ implements
    ┌───────────┼───────────┐
    │           │           │
┌─────────────┐ ┌─────────────────────────────┐
│FileDrawingDAO│ │     MySQLDrawingDAO         │
├─────────────┤ ├─────────────────────────────┤
│- baseDirectory│ │- dbUrl: String             │
│  : String   │ │- username: String          │
├─────────────┤ │- password: String          │
│+ save()     │ ├─────────────────────────────┤
│+ load()     │ │+ save(Drawing): void       │
│+ getAllNames│ │+ load(String): Drawing     │
│+ delete()   │ │+ getAllDrawingNames(): List│
└─────────────┘ │+ delete(String): void      │
                └─────────────────────────────┘
```

### 8. 🏛️ **Pattern MVC (Model-View-Controller)**

**Utilisation** : Architecture globale de l'application

**Objectif** : Séparer les responsabilités entre données, interface et logique.

#### Diagramme UML - Architecture MVC

```
┌─────────────────────────────┐
│           MODEL             │
├─────────────────────────────┤
│ • Shape (Rectangle, Circle, │
│   Line)                     │
│ • Drawing                   │
│ • Graph, Node, Edge         │
└─────────────────────────────┘
                │
                │ updates
                ▼
┌─────────────────────────────┐
│        CONTROLLER           │
├─────────────────────────────┤
│ • DrawingController         │
│ • GraphController           │
│ • CommandManager            │
└─────────────────────────────┘
                │
                │ manipulates
                ▼
┌─────────────────────────────┐
│           VIEW              │
├─────────────────────────────┤
│ • MainView                  │
│ • DrawingCanvas             │
│ • GraphPanel                │
└─────────────────────────────┘
```

---

## V. Conclusion

Ce projet démontre une implémentation complète et robuste d'une application de dessin utilisant les principaux design patterns. L'architecture MVC assure une séparation claire des responsabilités, tandis que les patterns utilisés apportent flexibilité, extensibilité et maintenabilité au code.

### Points Forts
- ✅ **Architecture solide** avec MVC
- ✅ **8 Design Patterns** bien implémentés
- ✅ **Persistance multiple** (fichier + base de données)
- ✅ **Interface intuitive** avec JavaFX
- ✅ **Fonctionnalités avancées** (undo/redo, décoration, graphes)

### Technologies Maîtrisées
- **Java** avec programmation orientée objet avancée
- **JavaFX** pour l'interface graphique
- **MySQL** pour la persistance
- **Design Patterns** pour une architecture robuste
```
```
