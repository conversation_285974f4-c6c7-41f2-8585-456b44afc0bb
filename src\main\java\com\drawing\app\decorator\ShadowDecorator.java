package com.drawing.app.decorator;

import com.drawing.app.model.Shape;
import com.drawing.app.model.Rectangle;
import com.drawing.app.model.Circle;
import com.drawing.app.model.Line;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

// Concrete Decorator - Adds shadow effect to shapes
public class ShadowDecorator extends ShapeDecorator {
    private final double shadowOffsetX;
    private final double shadowOffsetY;
    private final Color shadowColor;
    
    public ShadowDecorator(Shape decoratedShape) {
        this(decoratedShape, 5, 5, Color.GRAY);
    }
    
    public ShadowDecorator(Shape decoratedShape, double offsetX, double offsetY, Color shadowColor) {
        super(decoratedShape);
        this.shadowOffsetX = offsetX;
        this.shadowOffsetY = offsetY;
        this.shadowColor = shadowColor;
    }
    
    @Override
    protected void addDecoration(GraphicsContext gc) {
        // Save current graphics state
        gc.save();
        
        // Set shadow properties
        gc.setStroke(shadowColor);
        gc.setLineWidth(decoratedShape.getStrokeWidth());
        
        // Draw shadow based on shape type
        if (decoratedShape instanceof Rectangle) {
            drawRectangleShadow(gc, (Rectangle) decoratedShape);
        } else if (decoratedShape instanceof Circle) {
            drawCircleShadow(gc, (Circle) decoratedShape);
        } else if (decoratedShape instanceof Line) {
            drawLineShadow(gc, (Line) decoratedShape);
        }
        
        // Restore graphics state
        gc.restore();
    }
    
    private void drawRectangleShadow(GraphicsContext gc, Rectangle rect) {
        double x = rect.getX() + shadowOffsetX;
        double y = rect.getY() + shadowOffsetY;
        gc.strokeRect(x, y, rect.getWidth(), rect.getHeight());
    }
    
    private void drawCircleShadow(GraphicsContext gc, Circle circle) {
        double x = circle.getX() - circle.getRadius() + shadowOffsetX;
        double y = circle.getY() - circle.getRadius() + shadowOffsetY;
        double diameter = circle.getRadius() * 2;
        gc.strokeOval(x, y, diameter, diameter);
    }
    
    private void drawLineShadow(GraphicsContext gc, Line line) {
        double startX = line.getX() + shadowOffsetX;
        double startY = line.getY() + shadowOffsetY;
        double endX = line.getEndX() + shadowOffsetX;
        double endY = line.getEndY() + shadowOffsetY;
        gc.strokeLine(startX, startY, endX, endY);
    }
    
    // Getters for shadow properties
    public double getShadowOffsetX() {
        return shadowOffsetX;
    }
    
    public double getShadowOffsetY() {
        return shadowOffsetY;
    }
    
    public Color getShadowColor() {
        return shadowColor;
    }
}
