package com.drawing.app.decorator;

import com.drawing.app.model.Shape;
import com.drawing.app.model.Rectangle;
import com.drawing.app.model.Circle;
import com.drawing.app.model.Line;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

// Concrete Decorator - Adds glow effect to shapes
public class GlowDecorator extends ShapeDecorator {
    private final Color glowColor;
    private final int glowLayers;
    private final double glowIntensity;
    
    public GlowDecorator(Shape decoratedShape) {
        this(decoratedShape, Color.YELLOW, 3, 0.3);
    }
    
    public GlowDecorator(Shape decoratedShape, Color glowColor, int glowLayers, double glowIntensity) {
        super(decoratedShape);
        this.glowColor = glowColor;
        this.glowLayers = glowLayers;
        this.glowIntensity = Math.max(0.1, Math.min(1.0, glowIntensity));
    }
    
    @Override
    protected void addDecoration(GraphicsContext gc) {
        // Save current graphics state
        gc.save();
        
        // Draw multiple layers for glow effect
        for (int i = glowLayers; i > 0; i--) {
            double alpha = glowIntensity * (1.0 - (double) i / (glowLayers + 1));
            Color layerColor = Color.color(
                glowColor.getRed(),
                glowColor.getGreen(),
                glowColor.getBlue(),
                alpha
            );
            
            gc.setStroke(layerColor);
            gc.setLineWidth(decoratedShape.getStrokeWidth() + i * 2);
            
            // Draw glow layer based on shape type
            if (decoratedShape instanceof Rectangle) {
                drawRectangleGlow(gc, (Rectangle) decoratedShape, i);
            } else if (decoratedShape instanceof Circle) {
                drawCircleGlow(gc, (Circle) decoratedShape, i);
            } else if (decoratedShape instanceof Line) {
                drawLineGlow(gc, (Line) decoratedShape);
            }
        }
        
        // Restore graphics state
        gc.restore();
    }
    
    private void drawRectangleGlow(GraphicsContext gc, Rectangle rect, int layer) {
        double expansion = layer * 2;
        double x = rect.getX() - expansion;
        double y = rect.getY() - expansion;
        double width = rect.getWidth() + 2 * expansion;
        double height = rect.getHeight() + 2 * expansion;
        gc.strokeRect(x, y, width, height);
    }
    
    private void drawCircleGlow(GraphicsContext gc, Circle circle, int layer) {
        double expansion = layer * 2;
        double radius = circle.getRadius() + expansion;
        double x = circle.getX() - radius;
        double y = circle.getY() - radius;
        double diameter = radius * 2;
        gc.strokeOval(x, y, diameter, diameter);
    }
    
    private void drawLineGlow(GraphicsContext gc, Line line) {
        gc.strokeLine(line.getX(), line.getY(), line.getEndX(), line.getEndY());
    }
    
    // Getters for glow properties
    public Color getGlowColor() {
        return glowColor;
    }
    
    public int getGlowLayers() {
        return glowLayers;
    }
    
    public double getGlowIntensity() {
        return glowIntensity;
    }
}
