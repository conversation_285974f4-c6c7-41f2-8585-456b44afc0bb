package com.drawing.app.controller;

import com.drawing.app.command.CommandManager;
import com.drawing.app.command.DrawShapeCommand;
import com.drawing.app.decorator.DecoratorFactory;
import com.drawing.app.factory.ShapeFactory;
import com.drawing.app.logging.ConsoleLoggingStrategy;
import com.drawing.app.logging.DatabaseLoggingStrategy;
import com.drawing.app.logging.FileLoggingStrategy;
import com.drawing.app.logging.Logger;
import com.drawing.app.logging.LoggingStrategy;
import com.drawing.app.model.Drawing;
import com.drawing.app.model.Shape;
import com.drawing.app.observer.DrawingSubject;
import com.drawing.app.persistence.DrawingDAO;
import com.drawing.app.persistence.FileDrawingDAO;
import com.drawing.app.persistence.MySQLDrawingDAO;
import com.drawing.app.view.DrawingCanvas;
import com.drawing.app.view.MainView;

import javafx.scene.control.Alert;
import javafx.scene.control.ChoiceDialog;
import javafx.scene.control.TextInputDialog;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.paint.Color;

import java.util.List;
import java.util.Optional;

public class DrawingController extends DrawingSubject {
    private final MainView view;
    private final Drawing currentDrawing;
    private final CommandManager commandManager;
    private final Logger logger;
    private final DrawingDAO fileDAO;
    private final DrawingDAO databaseDAO;
    private ShapeFactory.ShapeType selectedShapeType;
    private Color selectedColor;
    private double selectedStrokeWidth;

    public DrawingController(MainView view) {
        this.view = view;
        this.currentDrawing = new Drawing();
        this.commandManager = CommandManager.getInstance(); // Singleton pattern
        this.selectedShapeType = ShapeFactory.ShapeType.RECTANGLE;
        this.selectedColor = Color.BLACK;
        this.selectedStrokeWidth = 2.0;

        // Initialize persistence layer
        this.fileDAO = new FileDrawingDAO();
        this.databaseDAO = initializeDatabaseDAO();

        // Initialize with console logging
        this.logger = new Logger(new ConsoleLoggingStrategy());
        addObserver(logger);

        setupEventHandlers();
        setupKeyboardShortcuts();
    }

    private void setupEventHandlers() {
        // Setup canvas
        DrawingCanvas canvas = view.getCanvas();
        canvas.setCurrentShapeType(selectedShapeType);
        canvas.setCurrentColor(selectedColor);
        canvas.setCurrentStrokeWidth(selectedStrokeWidth);
        // Set up shape creation callback
        canvas.setShapeCreationCallback(shape -> {
            DrawShapeCommand command = new DrawShapeCommand(currentDrawing, shape);
            commandManager.executeCommand(command);
            updateCanvas(); // Ensure canvas reflects the new state
            notifyShapeDrawn(shape.getClass().getSimpleName() + " created at (" +
                           shape.getX() + ", " + shape.getY() + ")");
        });
    }
    
    private void setupKeyboardShortcuts() {
        view.getRoot().setOnKeyPressed(this::handleKeyPressed);
        view.getRoot().setFocusTraversable(true);
        view.getRoot().requestFocus();
    }
      private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.DELETE || event.getCode() == KeyCode.BACK_SPACE) {
            view.getCanvas().deleteSelectedShape();
            notifyShapeDrawn("Shape deleted");
        } else if (event.isControlDown()) {
            switch (event.getCode()) {
                case Z:
                    undo();
                    break;
                case Y:
                    redo();
                    break;
                case N:
                    newDrawing();
                    break;
                case S:
                    saveDrawing();
                    break;
                case O:
                    openDrawing();
                    break;
                default:
                    break;
            }
        }
    }

    public void selectShape(ShapeFactory.ShapeType shapeType) {
        this.selectedShapeType = shapeType;
        view.getCanvas().setCurrentShapeType(shapeType);
        notifyShapeSelected(shapeType.toString());
    }

    public void selectColor(Color color) {
        this.selectedColor = color;
        view.getCanvas().setCurrentColor(color);
    }

    public void selectStrokeWidth(double width) {
        this.selectedStrokeWidth = width;
        view.getCanvas().setCurrentStrokeWidth(width);
    }

    public void setSelectedColor(Color color) {
        this.selectedColor = color;
        view.getCanvas().setCurrentColor(color);
    }

    public void setSelectedStrokeWidth(double width) {
        this.selectedStrokeWidth = width;
        view.getCanvas().setCurrentStrokeWidth(width);
    }

    public void undo() {
        commandManager.undo();
        updateCanvas();
        notifyShapeDrawn("Undo performed");
    }

    public void redo() {
        commandManager.redo();
        updateCanvas();
        notifyShapeDrawn("Redo performed");
    }

    public void clearDrawing() {
        currentDrawing.clear();
        updateCanvas();
        notifyShapeDrawn("Drawing cleared");
    }



    // Helper methods
    public void newDrawing() {
        currentDrawing.clear();
        updateCanvas();
        notifyDrawingSaved("New drawing created");
    }

    public void openDrawing() {
        List<String> availableDrawings = fileDAO.getAllDrawingNames();

        if (availableDrawings.isEmpty()) {
            showAlert("No saved drawings found.");
            return;
        }

        ChoiceDialog<String> dialog = new ChoiceDialog<>(availableDrawings.get(0), availableDrawings);
        dialog.setTitle("Open Drawing");
        dialog.setHeaderText("Select a drawing to open:");
        dialog.setContentText("Drawing:");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent()) {
            Drawing loadedDrawing = fileDAO.load(result.get());
            if (loadedDrawing != null) {
                currentDrawing.clear();
                currentDrawing.setName(loadedDrawing.getName());
                for (Shape shape : loadedDrawing.getShapes()) {
                    currentDrawing.addShape(shape);
                }
                updateCanvas();
                notifyDrawingLoaded("Drawing loaded: " + result.get());
            }
        }
    }

    public void saveDrawing() {
        if (currentDrawing.getName().equals("Untitled")) {
            saveAsDrawing();
        } else {
            saveDrawing(currentDrawing.getName());
        }
    }

    public void saveAsDrawing() {
        TextInputDialog dialog = new TextInputDialog(currentDrawing.getName());
        dialog.setTitle("Save Drawing As");
        dialog.setHeaderText("Enter a name for your drawing:");
        dialog.setContentText("Name:");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent() && !result.get().trim().isEmpty()) {
            String newName = result.get().trim();
            currentDrawing.setName(newName);
            saveDrawing(newName);
        }
    }

    private void saveDrawing(String filename) {
        try {
            // Try database first, fallback to file
            if (databaseDAO != null) {
                databaseDAO.save(currentDrawing);
                notifyDrawingSaved("Drawing saved to database: " + filename);
            } else {
                fileDAO.save(currentDrawing);
                notifyDrawingSaved("Drawing saved to file: " + filename);
            }
        } catch (Exception e) {
            // Fallback to file if database fails
            try {
                fileDAO.save(currentDrawing);
                notifyDrawingSaved("Drawing saved to file (database failed): " + filename);
            } catch (Exception fileError) {
                showAlert("Failed to save drawing: " + fileError.getMessage());
            }
        }
    }

    public void addShape(Shape shape) {
        currentDrawing.addShape(shape);
        updateCanvas();
        notifyShapeDrawn(shape.getClass().getSimpleName() + " added programmatically");
    }

    public Drawing getCurrentDrawing() {
        return currentDrawing;
    }

    public void updateCanvas() {
        view.getCanvas().setDrawing(currentDrawing);
    }

    public void setLoggingStrategy(String strategyType) {
        LoggingStrategy strategy;
        switch (strategyType.toLowerCase()) {
            case "console":
                strategy = new ConsoleLoggingStrategy();
                break;
            case "file":
                strategy = new FileLoggingStrategy("drawing_log.txt");
                break;
            case "database":
                strategy = new DatabaseLoggingStrategy(
                    "***************************************",
                    "root",
                    "password"
                );
                break;
            default:
                strategy = new ConsoleLoggingStrategy();
        }
        logger.setStrategy(strategy);
    }

    // Decorator Pattern Methods
    public void addShadowToSelectedShape() {
        Shape selectedShape = view.getCanvas().getSelectedShape();
        if (selectedShape != null) {
            Shape shadowShape = DecoratorFactory.getInstance()
                .createDecoratedShape(selectedShape, DecoratorFactory.DecorationType.SHADOW);
            replaceShapeInDrawing(selectedShape, shadowShape);
            notifyShapeDrawn("Shadow added to " + selectedShape.getClass().getSimpleName());
        }
    }

    public void addBorderToSelectedShape() {
        Shape selectedShape = view.getCanvas().getSelectedShape();
        if (selectedShape != null) {
            Shape borderShape = DecoratorFactory.getInstance()
                .createDecoratedShape(selectedShape, DecoratorFactory.DecorationType.BORDER);
            replaceShapeInDrawing(selectedShape, borderShape);
            notifyShapeDrawn("Border added to " + selectedShape.getClass().getSimpleName());
        }
    }

    public void addGlowToSelectedShape() {
        Shape selectedShape = view.getCanvas().getSelectedShape();
        if (selectedShape != null) {
            Shape glowShape = DecoratorFactory.getInstance()
                .createDecoratedShape(selectedShape, DecoratorFactory.DecorationType.GLOW);
            replaceShapeInDrawing(selectedShape, glowShape);
            notifyShapeDrawn("Glow added to " + selectedShape.getClass().getSimpleName());
        }
    }

    public void removeDecorationFromSelectedShape() {
        Shape selectedShape = view.getCanvas().getSelectedShape();
        if (selectedShape != null) {
            Shape undecorated = DecoratorFactory.getInstance().removeDecoration(selectedShape);
            if (undecorated != selectedShape) {
                replaceShapeInDrawing(selectedShape, undecorated);
                notifyShapeDrawn("Decoration removed from shape");
            }
        }
    }

    private void replaceShapeInDrawing(Shape oldShape, Shape newShape) {
        int index = currentDrawing.getShapes().indexOf(oldShape);
        if (index >= 0) {
            currentDrawing.removeShape(oldShape);
            currentDrawing.addShape(newShape);
            updateCanvas();
        }
    }

    private DrawingDAO initializeDatabaseDAO() {
        try {
            // Try to load MySQL driver first
            Class.forName("com.mysql.cj.jdbc.Driver");

            // Test connection
            MySQLDrawingDAO dao = new MySQLDrawingDAO(
                "***************************************",
                "root",
                "password"
            );
            System.out.println("Database connection successful - using MySQL storage");
            return dao;
        } catch (ClassNotFoundException e) {
            System.err.println("MySQL driver not found, using file storage only");
            return null;
        } catch (Exception e) {
            System.err.println("Database connection failed, using file storage only: " + e.getMessage());
            return null;
        }
    }

    private void showAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Drawing Application");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
