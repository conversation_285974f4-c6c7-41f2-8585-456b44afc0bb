package com.drawing.app.logging;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDateTime;

public class DatabaseLoggingStrategy implements LoggingStrategy {
    private Connection connection;
    private PreparedStatement insertStatement;
    private String dbUrl;
    private String username;
    private String password;
    private boolean isConnected = false;

    public DatabaseLoggingStrategy(String dbUrl, String username, String password) {
        this.dbUrl = dbUrl;
        this.username = username;
        this.password = password;
        initializeConnection();
    }

    private void initializeConnection() {
        try {
            // Try to load the MySQL driver
            Class.forName("com.mysql.cj.jdbc.Driver");

            // Establish connection
            connection = DriverManager.getConnection(dbUrl, username, password);
            createTableIfNotExists();
            insertStatement = connection.prepareStatement(
                "INSERT INTO logs (timestamp, message) VALUES (?, ?)"
            );
            isConnected = true;
            System.out.println("Database logging initialized successfully");
        } catch (ClassNotFoundException e) {
            System.err.println("MySQL driver not found. Please ensure mysql-connector-java is in classpath.");
            isConnected = false;
        } catch (SQLException e) {
            System.err.println("Error creating database logger: " + e.getMessage());
            System.err.println("Please check if MySQL is running and database 'drawing_app' exists.");
            isConnected = false;
        }
    }
    
    private void createTableIfNotExists() throws SQLException {
        String createTableSQL = "CREATE TABLE IF NOT EXISTS logs ("
            + "id INT AUTO_INCREMENT PRIMARY KEY,"
            + "timestamp DATETIME,"
            + "message TEXT"
            + ")";
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createTableSQL);
        }
    }
    
    @Override
    public void log(String message) {
        if (!isConnected) {
            System.err.println("Database not connected. Message: " + message);
            return;
        }

        if (insertStatement != null) {
            try {
                insertStatement.setTimestamp(1, Timestamp.valueOf(LocalDateTime.now()));
                insertStatement.setString(2, message);
                insertStatement.executeUpdate();
            } catch (SQLException e) {
                System.err.println("Error logging to database: " + e.getMessage());
                // Try to reconnect
                initializeConnection();
            }
        }
    }
    
    public boolean isConnected() {
        return isConnected;
    }

    @Override
    public void close() {
        try {
            if (insertStatement != null) insertStatement.close();
            if (connection != null) connection.close();
            isConnected = false;
        } catch (SQLException e) {
            System.err.println("Error closing database connection: " + e.getMessage());
        }
    }
}
