package com.drawing.app.logging;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * Test class to verify all logging strategies work correctly
 */
public class LoggingTest {
    
    public static void main(String[] args) {
        System.out.println("=== Testing Logging Strategies ===\n");
        
        testConsoleLogging();
        testFileLogging();
        testDatabaseLogging();
        
        System.out.println("\n=== Logging Tests Complete ===");
    }
    
    public static void testConsoleLogging() {
        System.out.println("1. Testing Console Logging:");
        System.out.println("   Expected: Messages should appear in console with timestamps");
        
        ConsoleLoggingStrategy consoleLogger = new ConsoleLoggingStrategy();
        consoleLogger.log("Test message 1 - Console logging");
        consoleLogger.log("Test message 2 - Console logging");
        consoleLogger.close();
        
        System.out.println("   ✓ Console logging test completed\n");
    }
    
    public static void testFileLogging() {
        System.out.println("2. Testing File Logging:");
        System.out.println("   Expected: Messages should be written to 'test_log.txt'");
        
        String testFileName = "test_log.txt";
        FileLoggingStrategy fileLogger = new FileLoggingStrategy(testFileName);
        fileLogger.log("Test message 1 - File logging");
        fileLogger.log("Test message 2 - File logging");
        fileLogger.close();
        
        // Verify file was created and contains messages
        File logFile = new File(testFileName);
        if (logFile.exists()) {
            try {
                List<String> lines = Files.readAllLines(Paths.get(testFileName));
                System.out.println("   ✓ File created successfully with " + lines.size() + " lines");
                System.out.println("   Content preview:");
                for (String line : lines) {
                    System.out.println("     " + line);
                }
            } catch (IOException e) {
                System.out.println("   ✗ Error reading log file: " + e.getMessage());
            }
        } else {
            System.out.println("   ✗ Log file was not created");
        }
        System.out.println();
    }
    
    public static void testDatabaseLogging() {
        System.out.println("3. Testing Database Logging:");
        System.out.println("   Expected: Messages should be stored in MySQL database");
        
        DatabaseLoggingStrategy dbLogger = new DatabaseLoggingStrategy(
            "***************************************",
            "root",
            ""  // Empty password - change this if your MySQL has a password
        );
        
        if (dbLogger.isConnected()) {
            System.out.println("   ✓ Database connection successful");
            dbLogger.log("Test message 1 - Database logging");
            dbLogger.log("Test message 2 - Database logging");
            System.out.println("   ✓ Test messages sent to database");
        } else {
            System.out.println("   ✗ Database connection failed");
            System.out.println("   Possible issues:");
            System.out.println("     - MySQL server not running");
            System.out.println("     - Database 'drawing_app' doesn't exist");
            System.out.println("     - Wrong credentials (username/password)");
            System.out.println("     - MySQL driver not in classpath");
        }
        
        dbLogger.close();
        System.out.println();
    }
}
